# Keep-Alive 缓存机制实现说明

## 实现概述

为 `/verify` 路由配置了 keep-alive 缓存机制，实现以下功能：

1. 当用户从 `/verify` 页面导航到其他页面后再返回时，页面保持之前的状态
2. 避免重新执行 `getCert` 函数，防止重复的证书获取请求
3. 在路由配置和组件层面正确设置 keep-alive

## 修改的文件

### 1. `src/global.d.ts`
- 添加了 `keepAlive?: boolean` 类型定义到 `RouteMeta` 接口

### 2. `src/router/index.ts`
- 为 `/verify` 路由添加了 `keepAlive: true` 标识

### 3. `src/App.vue`
- 重构了根组件，使用 Vue 3 推荐的 slot props 语法实现 keep-alive
- 配置了需要缓存的组件名称列表：`['VerifyPage']`
- 保持了原有的动态布局功能
- 修复了 Vue Router 警告，使用正确的 `<RouterView v-slot="{ Component }">` 语法

### 4. `src/views/verify/index.vue`
- 添加了组件名称定义：`defineOptions({ name: 'VerifyPage' })`
- 添加了 `isDataLoaded` 状态标记，用于跟踪数据加载状态
- 重构了数据初始化逻辑，提取为 `initData` 函数
- 添加了 `onActivated` 钩子，处理 keep-alive 组件激活时的逻辑
- 优化了 `getCert` 函数，添加了数据加载状态管理

## 工作原理

1. **首次访问 `/verify`**：
   - `onMounted` 钩子执行
   - 调用 `initData` 函数
   - 从 sessionStorage 获取 code
   - 调用 `getCert` 获取证书数据
   - 设置 `isDataLoaded = true`

2. **离开 `/verify` 页面**：
   - 组件实例被 keep-alive 缓存，不会销毁
   - 组件状态（包括 `certData`、`isDataLoaded` 等）得到保留

3. **再次访问 `/verify`**：
   - `onActivated` 钩子执行（而不是 `onMounted`）
   - 调用 `initData` 函数
   - 检查 code 是否变化和数据是否已加载
   - 如果 code 未变化且数据已加载，则跳过 `getCert` 调用
   - 如果 code 变化或数据未加载，则重新获取数据

## 关键特性

1. **智能缓存**：只有在必要时才重新获取数据
2. **状态保持**：页面状态（加载状态、错误状态、证书数据）完全保留
3. **性能优化**：避免重复的网络请求
4. **兼容性**：保持原有的错误处理和导航逻辑

## 测试建议

1. 访问 `/verify` 页面，等待数据加载完成
2. 导航到其他页面（如 `/guide`）
3. 返回 `/verify` 页面
4. 验证页面状态是否保持，且没有重新发起网络请求
5. 测试不同的 code 值，确保数据能正确更新

## 注意事项

- keep-alive 只对具有 `name` 属性的组件生效
- 组件名称必须与 `keepAliveIncludes` 数组中的名称完全匹配
- 如果需要为其他页面添加缓存，只需在 `keepAliveIncludes` 数组中添加对应的组件名称
