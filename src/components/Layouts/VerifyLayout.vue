<script setup lang="ts">
const route = useRoute()
const { pageTitle } = usePageTitle()
const title = computed<string>(() => pageTitle.value || route.meta.title || import.meta.env.VITE_TITLE)
</script>

<template>
  <div class="relative mx-auto h-screen max-w-500px min-w-320px flex flex-col overflow-hidden bg-white text-gray-700">
    <!-- 头部区域 -->
    <header class="relative h-13 flex-shrink-0 from-blue-500 to-blue-600 bg-gradient-to-b py-3 text-white shadow-blue-900/20 shadow-lg">
      <h1 class="text-center text-xl">
        {{ title }}
      </h1>
    </header>

    <!-- 内容区域 -->
    <slot />
  </div>
</template>
