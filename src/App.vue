<script setup lang="ts">
import { layouts } from '~/components/Layouts'

const route = useRoute()

// 根据路由 meta 动态选择布局
const layout = computed(() => {
  const layoutName = route.meta.layout || 'default'
  return layouts[layoutName]
})

// 需要缓存的组件名称列表（全局配置）
const keepAliveIncludes = ['VerifyPage'] // 需要缓存的组件名称
</script>

<template>
  <component :is="layout">
    <RouterView v-slot="{ Component }">
      <KeepAlive :include="keepAliveIncludes">
        <component :is="Component" />
      </KeepAlive>
    </RouterView>
  </component>
</template>
