<script setup lang="ts">
import EducationCert from './components/EducationCert.vue'

// 定义组件名称，用于 keep-alive 识别
defineOptions({
  name: 'VerifyPage',
})

const router = useRouter()
const code = ref<string>('')
const certData = reactive<Partial<CertData>>({})
const { setTitle } = usePageTitle()
const loading = ref<boolean>(true)
const notFound = ref<boolean>(false)
const errorMsg = ref<string>('')
const isDataLoaded = ref<boolean>(false) // 标记数据是否已加载

// 获取证书信息
async function getCert(code: string): Promise<void> {
  try {
    loading.value = true
    notFound.value = false
    errorMsg.value = ''

    const res: CertData = await ApiGetCert(code)

    if (!res || Object.keys(res).length === 0) {
      notFound.value = true
      errorMsg.value = '未查询到该证照信息'
      isDataLoaded.value = false
      return
    }

    useProcessData(certData, res)
    setTitle(res.title)
    isDataLoaded.value = true
  }

  catch (error: any) {
    notFound.value = true
    errorMsg.value = '未查询到该证照信息'
    isDataLoaded.value = false
  }
  finally {
    loading.value = false
  }
}

// 根据证书类型获取对应组件
const certComponent = computed<Component>(() => {
  const componentMap: Record<string, Component> = {
    [CertType.EDUCATION_PRIMARY]: EducationCert, // 小学毕业证
    [CertType.EDUCATION_MIDDLE]: EducationCert, // 初中毕业证
  }
  return componentMap[certData.type ?? CertType.EDUCATION_PRIMARY] || EducationCert
})

// 初始化数据的通用逻辑
async function initData(): Promise<void> {
  const currentCode = await useSessionStorage.getItem('code') as string

  if (!currentCode) {
    router.replace({ name: 'Index' })
    return
  }

  // 如果 code 发生变化或者数据未加载，则重新获取数据
  if (code.value !== currentCode || !isDataLoaded.value) {
    code.value = currentCode
    await getCert(currentCode)
  }
}

onMounted(async () => {
  await initData()
})

// keep-alive 组件激活时的钩子
onActivated(async () => {
  await initData()
})
</script>

<template>
  <!-- 状态显示区域（加载中或未查询到） -->
  <div v-if="loading || notFound" class="h-full flex flex-col items-center justify-center">
    <div class="relative mb-6 h-24 w-24">
      <div
        class="absolute inset-0 border-4 rounded-full"
        :class="loading ? 'border-blue-100' : 'border-orange-100'"
      />
      <div
        class="absolute inset-0 rounded-full"
        :class="loading ? 'animate-spin border-4 border-t-blue-500' : 'border-4 border-orange-200 opacity-50'"
      />
      <div class="absolute inset-0 flex items-center justify-center">
        <div v-if="loading" class="i-mingcute:certificate-line text-3xl text-blue-500" />
        <div v-else class="i-mingcute:alert-line text-3xl text-orange-500" />
      </div>
    </div>
    <p class="mb-2 text-lg font-medium" :class="loading ? 'text-blue-700' : 'text-orange-700'">
      {{ loading ? '正在加载' : '查询失败' }}
    </p>
    <p class="text-gray-500">
      {{ loading ? '请稍候，正在核验证照信息...' : (errorMsg || '请确认证照核验码是否正确') }}
    </p>
  </div>

  <!-- 使用动态组件显示证书模板 -->
  <component :is="certComponent" v-else :cert-data="certData" />
</template>
